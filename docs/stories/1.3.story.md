# Story 1.3: Epic 自动创建系统

## Status
Approved

## Story
**As a** product owner,
**I want** the system to automatically create Epic files from sharded PRD content,
**so that** development work can be properly organized.

## Acceptance Criteria
1. 系统能够从 docs/prd/ 分片文件中识别 Epic 定义
2. 系统能够为每个 Epic 创建独立的文件：docs/epics/epic-{N}.md
3. Epic 文件包含完整的目标描述和 Story 列表
4. 系统能够验证 Epic 的逻辑顺序和依赖关系
5. 提供 Epic 创建的状态报告和验证结果

## Tasks / Subtasks
- [ ] Task 1: 实现 Epic 识别和解析功能 (AC: 1)
  - [ ] 在 workflow/document_processor.py 中创建 extract_epics 方法
  - [ ] 解析 docs/prd/ 分片文件，识别 Epic 定义模式
  - [ ] 提取 Epic 标题、目标描述和 Story 列表
  - [ ] 处理 Epic 编号和命名规范
- [ ] Task 2: 创建 Epic 数据模型 (AC: 3)
  - [ ] 在 workflow/models.py 中定义 Epic 数据类
  - [ ] 包含属性：epic_id, title, description, stories, dependencies, priority
  - [ ] 添加类型提示和数据验证
  - [ ] 实现 Epic 到文件格式的转换方法
- [ ] Task 3: 实现 Epic 文件生成功能 (AC: 2, 3)
  - [ ] 创建 create_epic_files 方法
  - [ ] 生成标准化的 Epic markdown 文件格式
  - [ ] 确保文件命名符合 epic-{N}.md 规范
  - [ ] 创建 docs/epics/ 目录结构
- [ ] Task 4: 实现 Epic 验证和依赖检查 (AC: 4)
  - [ ] 创建 Epic 逻辑顺序验证算法
  - [ ] 检查 Epic 间的依赖关系合理性
  - [ ] 验证 Story 编号的连续性和唯一性
  - [ ] 生成验证报告和警告信息
- [ ] Task 5: 创建状态报告和索引文件 (AC: 5)
  - [ ] 生成 Epic 创建状态报告
  - [ ] 创建 docs/epics/index.md 索引文件
  - [ ] 包含 Epic 总览和导航链接
  - [ ] 提供创建统计和验证结果
- [ ] Task 6: 集成到 FastMCP 服务 (AC: 1-5)
  - [ ] 在 bmad_agent_mcp.py 中添加 create_epics MCP 工具
  - [ ] 实现工具接口和参数验证
  - [ ] 添加错误处理和日志记录
  - [ ] 更新 WorkflowState 中的 epic_progress 状态

## Dev Notes

### Previous Story Insights
基于故事 1.1 和 1.2 的实施：
- 需要与 WorkflowState 集成，更新 epic_progress 字段
- 依赖故事 1.2 的文档分片功能，确保 docs/prd/ 分片文件可用
- 遵循相同的错误处理和日志记录模式

### Data Models
**Epic 数据模型** [需要新定义]：
```python
@dataclass
class Epic:
    epic_id: str  # e.g., "1", "2"
    title: str    # Epic 标题
    description: str  # Epic 目标描述
    stories: List[Story]  # Story 列表
    dependencies: List[str]  # 依赖的其他 Epic ID
    priority: str  # 优先级 (高/中/低)
    estimated_days: str  # 预估时间
    status: str   # 状态 (待开始/进行中/完成)
```

**Story 子数据模型**：
```python
@dataclass
class Story:
    story_id: str  # e.g., "1.1", "1.2"
    title: str     # Story 标题
    user_story: str  # As a... I want... so that...
    acceptance_criteria: List[str]  # 验收标准列表
    priority: str   # 优先级
    estimated_days: str  # 预估时间
    dependencies: List[str]  # 依赖的其他 Story
```

### Component Specifications
**Document Processor** [Source: architecture/components.md#document-processor]
- 核心接口：
  - `extract_epics(prd_shards: list) -> list[Epic]`
  - `create_epic_files(epics: list) -> list[str]`
- 依赖：markdown-tree-parser, PyYAML, file system
- 技术栈：Python pathlib, regex for markdown parsing, YAML processing

### File Locations
基于项目结构 [Source: architecture/source-tree.md]：
- 主要实现：`workflow/document_processor.py`
- 数据模型：`workflow/models.py`
- MCP 集成：`bmad_agent_mcp.py`
- 输出目录：`docs/epics/`

### Epic 文件格式标准
基于现有 Epic 文件 [Source: docs/epics/epic-1.md]：
```markdown
# Epic {N}: {Epic 标题}

## Epic 目标
{Epic 目标描述}

## Story 列表

### Story {N}.{M}: {Story 标题}
**用户故事：** {As a... I want... so that...}

**验收标准：**
1. {验收标准1}
2. {验收标准2}
...

**优先级：** {高/中/低}
**估算：** {X-Y 天}
**依赖：** {依赖的 Story 或 Epic}

## Epic 完成标准
{Epic 完成的具体标准}

## 技术注意事项
{技术实施的注意事项}

## 风险和缓解措施
{识别的风险和对应的缓解措施}
```

### Technical Constraints
- Python 3.8+ 与严格的类型提示 [Source: architecture/coding-standards.md#core-standards]
- 使用 Python logging 模块，禁止 print() 语句 [Source: architecture/coding-standards.md#critical-rules]
- 所有文件操作必须包含适当的异常处理 [Source: architecture/coding-standards.md#critical-rules]
- 文件路径必须验证和清理 [Source: architecture/security.md#input-validation]

### Epic 解析规则
基于现有 PRD 结构分析：
- **Epic 识别模式**：在 PRD 分片中查找 "## Epic {N}:" 标题
- **Story 提取**：提取每个 Epic 下的 "### Story {N}.{M}:" 子章节
- **验收标准解析**：提取 "#### Acceptance Criteria" 下的编号列表
- **依赖关系识别**：解析 Story 描述中的依赖声明
- **优先级和估算**：从 Story 描述中提取优先级和时间估算信息

### Testing
**测试要求** [Source: architecture/test-strategy-and-standards.md]
- 框架：pytest 7.4+
- 位置：tests/unit/workflow/ 和 tests/integration/
- 覆盖率：新工作流程组件需要 90% 覆盖率
- 模拟：使用 unittest.mock 模拟文件系统操作
- **特殊测试场景**：
  - 复杂 PRD 结构的 Epic 提取准确性
  - Epic 依赖关系验证逻辑
  - 文件生成格式的正确性
  - 错误 PRD 格式的处理
  - Epic 编号冲突检测

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-12 | 1.0 | Initial story creation | SM Agent |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled by dev agent*

### Debug Log References
*To be filled by dev agent*

### Completion Notes List
*To be filled by dev agent*

### File List
*To be filled by dev agent*

## QA Results

### Review Date: 2025-08-12

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

**❌ 实现未完成**: 核心 Epic 功能尚未实现

**已完成组件**:
- ✅ 基础文档处理器: 已完成并通过测试
- ✅ 分片功能依赖: Story 1.2 已完成，提供必要的分片文件
- ✅ 最小化 Epic 生成: 在 `workflow_executor.py` 中有基础实现

**待完成组件**:
- ❌ `extract_epics` 方法: 未在 `workflow/document_processor.py` 中实现
- ❌ `create_epic_files` 方法: 未在 `workflow/document_processor.py` 中实现
- ❌ Epic 数据模型: 未在 `workflow/models.py` 中定义
- ❌ Epic 验证和依赖检查: 未实现
- ❌ MCP 工具集成: `create_epics` 工具未在 `bmad_agent_mcp.py` 中实现
- ❌ 完整的单元测试: Epic 相关功能测试缺失

### Task Completion Status

**Task 1**: ❌ Epic 识别和解析功能 - 未实现
**Task 2**: ❌ Epic 数据模型 - 未实现
**Task 3**: ❌ Epic 文件生成功能 - 未实现
**Task 4**: ❌ Epic 验证和依赖检查 - 未实现
**Task 5**: ❌ 状态报告和索引文件 - 未实现
**Task 6**: ❌ FastMCP 服务集成 - 未实现

### Acceptance Criteria Verification

1. ❌ **AC1**: 系统能够从 docs/prd/ 分片文件中识别 Epic 定义 - 未实现
2. ❌ **AC2**: 系统能够为每个 Epic 创建独立的文件：docs/epics/epic-{N}.md - 未实现
3. ❌ **AC3**: Epic 文件包含完整的目标描述和 Story 列表 - 未实现
4. ❌ **AC4**: 系统能够验证 Epic 的逻辑顺序和依赖关系 - 未实现
5. ❌ **AC5**: 提供 Epic 创建的状态报告和验证结果 - 未实现

### Recommendations

1. **优先实现核心功能**:
   - 在 `workflow/document_processor.py` 中实现 `extract_epics` 和 `create_epic_files` 方法
   - 在 `workflow/models.py` 中定义 Epic 和 Story 数据类

2. **完善测试覆盖**:
   - 为 Epic 提取和生成功能添加单元测试
   - 添加集成测试验证端到端流程

3. **MCP 工具集成**:
   - 在 `bmad_agent_mcp.py` 中添加 `create_epics` MCP 工具

### Final Status

**❌ NOT IMPLEMENTED - REQUIRES DEVELOPMENT**

Story 1.3 的核心功能尚未实现，需要完整的开发工作才能满足验收标准。

(QA by Quinn, Senior QA Agent - Updated 2025-08-12)
