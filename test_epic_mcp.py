#!/usr/bin/env python3
"""
Quick test for Epic MCP tool functionality
"""

import tempfile
import sys
from pathlib import Path

# Add project root to path
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

from workflow.document_processor import extract_epics, create_epic_files

def test_create_epics_mcp_tool():
    """Test the create_epics MCP tool end-to-end."""
    
    # Create temporary directory structure
    with tempfile.TemporaryDirectory() as tmp_dir:
        tmp_path = Path(tmp_dir)
        
        # Create PRD shards directory
        prd_dir = tmp_path / "docs" / "prd"
        prd_dir.mkdir(parents=True)
        
        # Create test PRD shard with Epic content
        test_content = """# Product Requirements Document

## Epic 1: User Management System
This epic covers user registration, authentication, and profile management.

### Story 1.1: User Registration
**用户故事：** As a new user, I want to register an account so that I can access the system.

**验收标准：**
1. User can enter email and password
2. System validates email format
3. Password meets security requirements
4. Confirmation email is sent

### Story 1.2: User Login
**用户故事：** As a registered user, I want to log in so that I can access my account.

**验收标准：**
1. User can enter credentials
2. System validates credentials
3. User is redirected to dashboard

## Epic 2: Content Management
This epic covers content creation and management features.

### Story 2.1: Create Content
**用户故事：** As a content creator, I want to create new content so that I can share information.

**验收标准：**
1. User can create new content
2. Content is saved properly
3. Content can be published
"""
        
        shard_file = prd_dir / "features.md"
        shard_file.write_text(test_content, encoding="utf-8")
        
        # Test the MCP tool
        result = create_epics(
            prd_shards_dir=str(prd_dir),
            output_dir=str(tmp_path / "docs" / "epics")
        )
        
        # Verify results
        print("MCP Tool Result:")
        print(f"Success: {result['success']}")
        print(f"Epics Count: {result['epics_count']}")
        print(f"Epic Files: {result['epic_files']}")
        print(f"Validation Results: {result['validation_results']}")
        print(f"Errors: {result['errors']}")
        
        # Assertions
        assert result['success'] is True
        assert result['epics_count'] == 2
        assert len(result['epic_files']) == 2
        assert result['validation_results']['epics_found'] == 2
        assert result['validation_results']['files_generated'] == 2
        assert result['validation_results']['validation_passed'] is True
        assert len(result['errors']) == 0
        
        # Verify files were created
        epics_dir = tmp_path / "docs" / "epics"
        assert epics_dir.exists()
        
        epic1_file = epics_dir / "epic-1.md"
        epic2_file = epics_dir / "epic-2.md"
        index_file = epics_dir / "index.md"
        
        assert epic1_file.exists()
        assert epic2_file.exists()
        assert index_file.exists()
        
        # Verify file content
        epic1_content = epic1_file.read_text(encoding="utf-8")
        assert "# Epic 1: User Management System" in epic1_content
        assert "## Epic 目标" in epic1_content
        assert "user registration, authentication" in epic1_content.lower()
        assert "### Story 1.1: User Registration" in epic1_content
        assert "As a new user, I want to register" in epic1_content
        assert "1. User can enter email and password" in epic1_content
        
        print("\n✅ All tests passed! Epic MCP tool is working correctly.")

if __name__ == "__main__":
    test_create_epics_mcp_tool()
