import shutil
from pathlib import Path
import tempfile

from workflow.document_processor import extract_epics, create_epic_files
from workflow.models import Epic, Story

def _write_shard(path: Path, content: str):
    path.parent.mkdir(parents=True, exist_ok=True)
    path.write_text(content, encoding="utf-8")

def test_extract_epics_simple(tmp_path):
    # prepare a shard with one epic and two stories
    docs_dir = tmp_path / "docs" / "prd"
    docs_dir.mkdir(parents=True, exist_ok=True)
    shard = docs_dir / "epics.md"
    content = """# PRD intro

## Epic 1: First Epic
This epic's goal is to provide basic functionality.

### Story 1.1: Implement feature A
**用户故事：** As a user I want feature A so that I can do X.
**验收标准：**
1. Does A
2. Does B

### Story 1.2: Implement feature B
As a user I want feature B so that I can do Y.
1. Works reliably
"""
    _write_shard(shard, content)

    epics = extract_epics([str(shard)])
    assert isinstance(epics, list)
    assert len(epics) == 1
    epic = epics[0]
    assert epic.epic_id == "1"
    assert "First Epic" in epic.title
    assert len(epic.stories) == 2
    s1, s2 = epic.stories
    assert s1.story_id == "1.1"
    assert "feature A" in s1.title.lower() or "feature a" in s1.title.lower()
    assert isinstance(s1.acceptance_criteria, list)
    assert len(s1.acceptance_criteria) >= 2

def test_create_epic_files_and_index(tmp_path):
    # create two Epic dataclasses and write them out
    out_dir = tmp_path / "docs" / "epics"
    e1 = Epic(epic_id="1", title="Alpha Epic", description="Goal A", stories=[
        Story(story_id="1.1", title="S1", user_story="As a..."), 
    ])
    e2 = Epic(epic_id="2", title="Beta Epic", description="Goal B", stories=[
        Story(story_id="2.1", title="S2", user_story="As a..."), 
    ])
    generated = create_epic_files([e1, e2], str(out_dir))
    # check generated files exist
    assert len(generated) >= 2
    for p in generated:
        pth = Path(p)
        assert pth.exists()
        text = pth.read_text(encoding="utf-8")
        assert f"Epic {('1' if 'epic-1' in pth.name else '2')}:" in text

    # check index
    index = out_dir / "index.md"
    assert index.exists()
    idx_text = index.read_text(encoding="utf-8")
    assert "Epics" in idx_text
    assert "epic-1.md" in idx_text or "epic-2.md" in idx_text