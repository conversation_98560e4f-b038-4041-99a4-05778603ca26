"""
代码生成引擎
实现基于 Story 和 Task 的代码生成功能
"""

import ast
import logging
import os
import subprocess
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import jinja2

from .models import CodeGenerationResult, FileSpec, TestGenerationResult, TestCase

logger = logging.getLogger(__name__)


class CodeGenerator:
    """核心代码生成引擎"""
    
    def __init__(self, project_root: str, template_dir: Optional[str] = None):
        """
        初始化代码生成器
        
        Args:
            project_root: 项目根目录路径
            template_dir: 模板目录路径，默认为 workflow/templates
        """
        self.project_root = Path(project_root)
        self.template_dir = Path(template_dir) if template_dir else self.project_root / "workflow" / "templates"
        
        # 初始化 Jinja2 环境
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(self.template_dir)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 简单模板缓存，避免重复从磁盘加载模板（keyed by template relative path）
        self._template_cache: Dict[str, jinja2.Template] = {}
        # 支持的编程语言
        self.supported_languages = {
            'python': {
                'extension': '.py',
                'template_dir': 'python',
                'formatter': 'black',
                'linter': 'flake8',
                'type_checker': 'mypy'
            },
            'javascript': {
                'extension': '.js',
                'template_dir': 'javascript',
                'formatter': None,
                'linter': None,
                'type_checker': None
            },
            'typescript': {
                'extension': '.ts',
                'template_dir': 'typescript',
                'formatter': None,
                'linter': None,
                'type_checker': None
            }
        }
        
        logger.info(f"CodeGenerator initialized with project_root: {self.project_root}")
    
    def generate_code(self, story: Dict[str, Any], task: Dict[str, Any]) -> CodeGenerationResult:
        """
        基于 Story 和 Task 生成代码
        
        Args:
            story: Story 数据
            task: Task 数据
            
        Returns:
            CodeGenerationResult: 代码生成结果
        """
        start_time = time.time()
        logger.info(f"Starting code generation for story: {story.get('id', 'unknown')}, task: {task.get('name', 'unknown')}")
        
        try:
            # 解析任务要求，生成文件规格
            file_specs = self._parse_task_requirements(story, task)
            
            # 生成代码文件
            generated_files = []
            modified_files = []
            deleted_files = []
            errors = []
            warnings = []
            
            for spec in file_specs:
                try:
                    if spec.operation == 'create':
                        result_files = self.create_files([spec])
                        generated_files.extend(result_files)
                    elif spec.operation == 'modify':
                        result_files = self.modify_files([spec])
                        modified_files.extend(result_files)
                    elif spec.operation == 'delete':
                        self._delete_file(spec.path)
                        deleted_files.append(spec.path)
                except Exception as e:
                    error_msg = f"Failed to {spec.operation} file {spec.path}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            # 代码质量验证
            quality_metrics = {}
            validation_results = {}
            
            if generated_files or modified_files:
                all_files = generated_files + modified_files
                quality_metrics = self._analyze_code_quality(all_files)
                validation_results = self._validate_code(all_files)
            
            execution_time = time.time() - start_time
            success = len(errors) == 0
            
            result = CodeGenerationResult(
                success=success,
                generated_files=generated_files,
                modified_files=modified_files,
                deleted_files=deleted_files,
                operation_type=task.get('operation_type', 'create'),
                quality_metrics=quality_metrics,
                validation_results=validation_results,
                errors=errors,
                warnings=warnings,
                execution_time=execution_time
            )
            
            logger.info(f"Code generation completed. Success: {success}, Files: {len(generated_files + modified_files + deleted_files)}")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Code generation failed: {str(e)}"
            logger.error(error_msg)
            
            return CodeGenerationResult(
                success=False,
                generated_files=[],
                modified_files=[],
                deleted_files=[],
                operation_type=task.get('operation_type', 'create'),
                quality_metrics={},
                validation_results={},
                errors=[error_msg],
                warnings=[],
                execution_time=execution_time
            )
    
    def create_files(self, file_specs: List[FileSpec]) -> List[str]:
        """
        创建新文件
        
        Args:
            file_specs: 文件规格列表
            
        Returns:
            List[str]: 创建的文件路径列表
        """
        created_files = []
        
        for spec in file_specs:
            try:
                file_path = self.project_root / spec.path
                
                # 确保目录存在
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 生成文件内容
                if spec.template:
                    content = self._render_template(spec.template, spec.parameters, spec.language)
                else:
                    content = spec.content
                
                # 写入文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                created_files.append(spec.path)
                logger.info(f"Created file: {spec.path}")
                
            except Exception as e:
                logger.error(f"Failed to create file {spec.path}: {str(e)}")
                raise
        
        return created_files
    
    def modify_files(self, modifications: List[FileSpec]) -> List[str]:
        """
        修改现有文件
        
        Args:
            modifications: 修改规格列表
            
        Returns:
            List[str]: 修改的文件路径列表
        """
        modified_files = []
        
        for spec in modifications:
            try:
                file_path = self.project_root / spec.path
                
                if not file_path.exists():
                    logger.warning(f"File {spec.path} does not exist, creating new file")
                    self.create_files([spec])
                    modified_files.append(spec.path)
                    continue
                
                # 读取现有内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
                
                # 应用修改
                if spec.language == 'python':
                    new_content = self._modify_python_file(existing_content, spec)
                else:
                    # 对于非 Python 文件，直接替换内容
                    if spec.template:
                        new_content = self._render_template(spec.template, spec.parameters, spec.language)
                    else:
                        new_content = spec.content
                
                # 写入修改后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                modified_files.append(spec.path)
                logger.info(f"Modified file: {spec.path}")
                
            except Exception as e:
                logger.error(f"Failed to modify file {spec.path}: {str(e)}")
                raise
        
        return modified_files
    
    def _parse_task_requirements(self, story: Dict[str, Any], task: Dict[str, Any]) -> List[FileSpec]:
        """
        解析任务要求，生成文件规格
        
        Args:
            story: Story 数据
            task: Task 数据
            
        Returns:
            List[FileSpec]: 文件规格列表
        """
        file_specs = []
        
        # 基于任务描述推断需要创建的文件
        task_name = task.get('name', '')
        task_description = task.get('description', '')
        
        # 简单的规则匹配来确定文件类型和位置
        if 'CodeGenerator' in task_description or 'code_generator.py' in task_description:
            file_specs.append(FileSpec(
                path='workflow/code_generator.py',
                content='',
                language='python',
                template='python/class.py.j2',
                parameters={
                    'class_name': 'CodeGenerator',
                    'description': '代码生成引擎',
                    'methods': ['generate_code', 'create_files', 'modify_files']
                },
                operation='create'
            ))
        
        return file_specs
    
    def _render_template(self, template_name: str, parameters: Dict[str, Any], language: str) -> str:
        """
        渲染模板
        
        Args:
            template_name: 模板名称
            parameters: 模板参数
            language: 编程语言
            
        Returns:
            str: 渲染后的内容
        """
        try:
            # 构建完整的模板路径
            lang_config = self.supported_languages.get(language, {})
            template_dir = lang_config.get('template_dir', language)
            full_template_path = f"{template_dir}/{template_name}"
            
            # 使用缓存以减少磁盘 I/O 和模板解析开销
            template = self._template_cache.get(full_template_path)
            if template is None:
                template = self.jinja_env.get_template(full_template_path)
                # 将模板缓存在内存中
                self._template_cache[full_template_path] = template
            
            return template.render(**parameters)
            
        except Exception as e:
            logger.error(f"Failed to render template {template_name}: {str(e)}")
            raise
    
    def _modify_python_file(self, existing_content: str, spec: FileSpec) -> str:
        """
        使用 AST 修改 Python 文件
        
        Args:
            existing_content: 现有文件内容
            spec: 文件规格
            
        Returns:
            str: 修改后的内容
        """
        try:
            # 解析现有代码的 AST
            tree = ast.parse(existing_content)
            
            # 这里可以实现更复杂的 AST 操作
            # 目前简单返回新内容
            if spec.template:
                return self._render_template(spec.template, spec.parameters, spec.language)
            else:
                return spec.content
                
        except Exception as e:
            logger.error(f"Failed to modify Python file using AST: {str(e)}")
            # 回退到简单替换
            if spec.template:
                return self._render_template(spec.template, spec.parameters, spec.language)
            else:
                return spec.content
    
    def delete_files(self, file_paths: List[str]) -> List[str]:
        """
        删除多个文件

        Args:
            file_paths: 文件路径列表

        Returns:
            List[str]: 成功删除的文件路径列表
        """
        deleted_files = []

        for file_path in file_paths:
            try:
                self._delete_file(file_path)
                deleted_files.append(file_path)
            except Exception as e:
                logger.error(f"Failed to delete file {file_path}: {str(e)}")
                raise

        return deleted_files

    def rename_file(self, old_path: str, new_path: str) -> bool:
        """
        重命名文件

        Args:
            old_path: 原文件路径
            new_path: 新文件路径

        Returns:
            bool: 是否成功重命名
        """
        try:
            old_full_path = self.project_root / old_path
            new_full_path = self.project_root / new_path

            if not old_full_path.exists():
                logger.error(f"Source file {old_path} does not exist")
                return False

            # 确保目标目录存在
            new_full_path.parent.mkdir(parents=True, exist_ok=True)

            # 重命名文件
            old_full_path.rename(new_full_path)
            logger.info(f"Renamed file from {old_path} to {new_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to rename file from {old_path} to {new_path}: {str(e)}")
            return False

    def backup_files(self, file_paths: List[str], backup_dir: str = "backups") -> Dict[str, str]:
        """
        备份文件

        Args:
            file_paths: 要备份的文件路径列表
            backup_dir: 备份目录

        Returns:
            Dict[str, str]: 原文件路径到备份文件路径的映射
        """
        backup_mapping = {}
        backup_root = self.project_root / backup_dir
        backup_root.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for file_path in file_paths:
            try:
                source_path = self.project_root / file_path
                if not source_path.exists():
                    logger.warning(f"File {file_path} does not exist, skipping backup")
                    continue

                # 创建备份文件名
                backup_filename = f"{source_path.stem}_{timestamp}{source_path.suffix}"
                backup_path = backup_root / backup_filename

                # 复制文件
                import shutil
                shutil.copy2(source_path, backup_path)

                backup_mapping[file_path] = str(backup_path.relative_to(self.project_root))
                logger.info(f"Backed up {file_path} to {backup_mapping[file_path]}")

            except Exception as e:
                logger.error(f"Failed to backup file {file_path}: {str(e)}")
                raise

        return backup_mapping

    def rollback_files(self, backup_mapping: Dict[str, str]) -> List[str]:
        """
        回滚文件操作

        Args:
            backup_mapping: 备份映射（原文件路径 -> 备份文件路径）

        Returns:
            List[str]: 成功回滚的文件路径列表
        """
        rolled_back_files = []

        for original_path, backup_path in backup_mapping.items():
            try:
                source_path = self.project_root / backup_path
                target_path = self.project_root / original_path

                if not source_path.exists():
                    logger.error(f"Backup file {backup_path} does not exist")
                    continue

                # 确保目标目录存在
                target_path.parent.mkdir(parents=True, exist_ok=True)

                # 复制备份文件回原位置
                import shutil
                shutil.copy2(source_path, target_path)

                rolled_back_files.append(original_path)
                logger.info(f"Rolled back {original_path} from {backup_path}")

            except Exception as e:
                logger.error(f"Failed to rollback file {original_path}: {str(e)}")
                raise

        return rolled_back_files

    def _delete_file(self, file_path: str) -> None:
        """
        删除单个文件

        Args:
            file_path: 文件路径
        """
        try:
            full_path = self.project_root / file_path
            if full_path.exists():
                full_path.unlink()
                logger.info(f"Deleted file: {file_path}")
            else:
                logger.warning(f"File {file_path} does not exist")
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {str(e)}")
            raise
    
    def _analyze_code_quality(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        分析代码质量
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            Dict[str, Any]: 质量指标
        """
        metrics = {
            'total_files': len(file_paths),
            'python_files': 0,
            'total_lines': 0,
            'complexity_score': 0
        }
        
        for file_path in file_paths:
            full_path = self.project_root / file_path
            if full_path.suffix == '.py':
                metrics['python_files'] += 1
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        metrics['total_lines'] += len(content.splitlines())
                        
                        # 简单的复杂度分析
                        tree = ast.parse(content)
                        complexity = self._calculate_complexity(tree)
                        metrics['complexity_score'] += complexity
                        
                except Exception as e:
                    logger.warning(f"Failed to analyze file {file_path}: {str(e)}")
        
        return metrics
    
    def analyze_code_structure(self, file_path: str) -> Dict[str, Any]:
        """
        分析代码结构

        Args:
            file_path: 文件路径

        Returns:
            Dict[str, Any]: 代码结构分析结果
        """
        try:
            full_path = self.project_root / file_path
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)

            analysis = {
                'classes': [],
                'functions': [],
                'imports': [],
                'complexity': self._calculate_complexity(tree),
                'lines_of_code': len(content.splitlines()),
                'docstring_coverage': 0,
                'dependencies': []
            }

            # 分析类
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'line_number': node.lineno,
                        'methods': [],
                        'docstring': ast.get_docstring(node),
                        'complexity': self._calculate_complexity(node)
                    }

                    # 分析类方法
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_info = {
                                'name': item.name,
                                'line_number': item.lineno,
                                'args': [arg.arg for arg in item.args.args],
                                'docstring': ast.get_docstring(item),
                                'complexity': self._calculate_complexity(item)
                            }
                            class_info['methods'].append(method_info)

                    analysis['classes'].append(class_info)

                elif isinstance(node, ast.FunctionDef) and not any(isinstance(parent, ast.ClassDef) for parent in ast.walk(tree) if hasattr(parent, 'body') and node in getattr(parent, 'body', [])):
                    # 只处理顶级函数
                    function_info = {
                        'name': node.name,
                        'line_number': node.lineno,
                        'args': [arg.arg for arg in node.args.args],
                        'docstring': ast.get_docstring(node),
                        'complexity': self._calculate_complexity(node)
                    }
                    analysis['functions'].append(function_info)

                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis['imports'].append({
                                'module': alias.name,
                                'alias': alias.asname,
                                'type': 'import'
                            })
                    else:  # ImportFrom
                        for alias in node.names:
                            analysis['imports'].append({
                                'module': node.module,
                                'name': alias.name,
                                'alias': alias.asname,
                                'type': 'from_import'
                            })

            # 计算文档字符串覆盖率
            documented_items = 0
            total_items = len(analysis['classes']) + len(analysis['functions'])

            for cls in analysis['classes']:
                if cls['docstring']:
                    documented_items += 1
                for method in cls['methods']:
                    total_items += 1
                    if method['docstring']:
                        documented_items += 1

            for func in analysis['functions']:
                if func['docstring']:
                    documented_items += 1

            if total_items > 0:
                analysis['docstring_coverage'] = documented_items / total_items

            # 提取依赖关系
            analysis['dependencies'] = list(set([imp['module'] for imp in analysis['imports'] if imp['module']]))

            return analysis

        except Exception as e:
            logger.error(f"Failed to analyze code structure for {file_path}: {str(e)}")
            return {
                'error': str(e),
                'classes': [],
                'functions': [],
                'imports': [],
                'complexity': 0,
                'lines_of_code': 0,
                'docstring_coverage': 0,
                'dependencies': []
            }

    def analyze_code_dependencies(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        分析代码依赖关系

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 依赖关系分析结果
        """
        dependency_graph = {}
        all_dependencies = set()

        for file_path in file_paths:
            if file_path.endswith('.py'):
                analysis = self.analyze_code_structure(file_path)
                dependencies = analysis.get('dependencies', [])
                dependency_graph[file_path] = dependencies
                all_dependencies.update(dependencies)

        return {
            'dependency_graph': dependency_graph,
            'all_dependencies': list(all_dependencies),
            'internal_dependencies': [dep for dep in all_dependencies if not self._is_external_dependency(dep)],
            'external_dependencies': [dep for dep in all_dependencies if self._is_external_dependency(dep)]
        }

    def suggest_code_improvements(self, file_path: str) -> List[Dict[str, Any]]:
        """
        提供代码改进建议

        Args:
            file_path: 文件路径

        Returns:
            List[Dict[str, Any]]: 改进建议列表
        """
        suggestions = []
        analysis = self.analyze_code_structure(file_path)

        # 复杂度建议
        if analysis['complexity'] > 10:
            suggestions.append({
                'type': 'complexity',
                'severity': 'warning',
                'message': f"File has high complexity ({analysis['complexity']}). Consider refactoring.",
                'suggestion': 'Break down complex functions into smaller, more focused functions.'
            })

        # 文档字符串覆盖率建议
        if analysis['docstring_coverage'] < 0.8:
            suggestions.append({
                'type': 'documentation',
                'severity': 'info',
                'message': f"Low docstring coverage ({analysis['docstring_coverage']:.1%}). Consider adding more documentation.",
                'suggestion': 'Add docstrings to classes and functions to improve code maintainability.'
            })

        # 函数复杂度建议
        for func in analysis['functions']:
            if func['complexity'] > 5:
                suggestions.append({
                    'type': 'function_complexity',
                    'severity': 'warning',
                    'message': f"Function '{func['name']}' has high complexity ({func['complexity']}).",
                    'suggestion': f"Consider refactoring function '{func['name']}' to reduce complexity.",
                    'line_number': func['line_number']
                })

        # 类方法复杂度建议
        for cls in analysis['classes']:
            for method in cls['methods']:
                if method['complexity'] > 5:
                    suggestions.append({
                        'type': 'method_complexity',
                        'severity': 'warning',
                        'message': f"Method '{cls['name']}.{method['name']}' has high complexity ({method['complexity']}).",
                        'suggestion': f"Consider refactoring method '{method['name']}' in class '{cls['name']}'.",
                        'line_number': method['line_number']
                    })

        return suggestions

    def _is_external_dependency(self, module_name: str) -> bool:
        """
        判断是否为外部依赖

        Args:
            module_name: 模块名称

        Returns:
            bool: 是否为外部依赖
        """
        if not module_name:
            return False

        # Python 标准库模块
        stdlib_modules = {
            'os', 'sys', 'json', 'datetime', 'pathlib', 'typing', 'dataclasses',
            'asyncio', 'logging', 'subprocess', 'tempfile', 'shutil', 'ast',
            'time', 're', 'uuid', 'collections', 'itertools', 'functools'
        }

        # 检查是否为标准库模块
        root_module = module_name.split('.')[0]
        if root_module in stdlib_modules:
            return False

        # 检查是否为项目内部模块
        if module_name.startswith('workflow') or module_name.startswith('.'):
            return False

        return True

    def _calculate_complexity(self, tree: ast.AST) -> int:
        """
        计算代码复杂度（循环复杂度）

        Args:
            tree: AST 树

        Returns:
            int: 复杂度分数
        """
        complexity = 1  # 基础复杂度

        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.Try):
                complexity += 1
                # 每个 except 子句增加复杂度
                complexity += len(node.handlers)
            elif isinstance(node, (ast.With, ast.AsyncWith)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                # 布尔操作符（and, or）增加复杂度
                complexity += len(node.values) - 1

        return complexity
    
    def apply_code_formatting(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        应用代码格式化

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 格式化结果
        """
        results = {
            'success': True,
            'formatted_files': [],
            'errors': [],
            'warnings': []
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        if python_files:
            # 应用 black 格式化
            try:
                cmd = ['black'] + [str(self.project_root / f) for f in python_files]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    results['formatted_files'].extend(python_files)
                    logger.info(f"Successfully formatted {len(python_files)} Python files")
                else:
                    results['success'] = False
                    results['errors'].append(f"Black formatting failed: {result.stderr}")
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                results['success'] = False
                results['errors'].append(f"Black formatting failed: {str(e)}")

        return results

    def run_code_linting(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        运行代码 lint 检查

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: Lint 结果
        """
        results = {
            'success': True,
            'issues': [],
            'errors': [],
            'warnings': []
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        if python_files:
            # 运行 flake8
            try:
                cmd = ['flake8', '--max-line-length=88'] + [str(self.project_root / f) for f in python_files]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    logger.info(f"Flake8 check passed for {len(python_files)} files")
                else:
                    results['success'] = False
                    issues = result.stdout.strip().split('\n') if result.stdout.strip() else []
                    results['issues'].extend(issues)
                    results['warnings'].append(f"Flake8 found {len(issues)} issues")
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                results['errors'].append(f"Flake8 check failed: {str(e)}")

        return results

    def run_type_checking(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        运行类型检查

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 类型检查结果
        """
        results = {
            'success': True,
            'issues': [],
            'errors': [],
            'warnings': []
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        if python_files:
            # 运行 mypy
            try:
                cmd = ['mypy', '--ignore-missing-imports'] + [str(self.project_root / f) for f in python_files]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    logger.info(f"MyPy type check passed for {len(python_files)} files")
                else:
                    results['success'] = False
                    issues = result.stdout.strip().split('\n') if result.stdout.strip() else []
                    results['issues'].extend(issues)
                    results['warnings'].append(f"MyPy found {len(issues)} type issues")
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                results['errors'].append(f"MyPy type check failed: {str(e)}")

        return results

    def validate_custom_rules(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        验证自定义编码规则

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 自定义规则验证结果
        """
        results = {
            'success': True,
            'violations': [],
            'errors': [],
            'warnings': []
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        for file_path in python_files:
            try:
                full_path = self.project_root / file_path
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查禁止使用 print() 语句
                if 'print(' in content:
                    results['success'] = False
                    results['violations'].append(f"{file_path}: Found print() statement - use logging instead")

                # 检查是否有类型提示
                try:
                    tree = ast.parse(content)
                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            # 检查函数是否有返回类型提示
                            if node.returns is None and node.name != '__init__':
                                results['warnings'].append(f"{file_path}: Function '{node.name}' missing return type hint")

                            # 检查参数是否有类型提示
                            for arg in node.args.args:
                                if arg.annotation is None and arg.arg != 'self':
                                    results['warnings'].append(f"{file_path}: Parameter '{arg.arg}' in function '{node.name}' missing type hint")

                except SyntaxError:
                    # 语法错误会在其他地方处理
                    pass

            except Exception as e:
                results['errors'].append(f"Failed to validate custom rules for {file_path}: {str(e)}")

        return results

    def _validate_code(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        验证代码质量（综合验证）

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 验证结果
        """
        results = {
            'syntax_valid': True,
            'format_valid': True,
            'lint_valid': True,
            'type_valid': True,
            'custom_rules_valid': True,
            'errors': [],
            'warnings': [],
            'details': {}
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        if python_files:
            # 语法检查
            for file_path in python_files:
                full_path = self.project_root / file_path
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    ast.parse(content)
                except SyntaxError as e:
                    results['syntax_valid'] = False
                    results['errors'].append(f"Syntax error in {file_path}: {str(e)}")

            # 格式化检查
            format_results = self.apply_code_formatting(python_files)
            results['format_valid'] = format_results['success']
            results['details']['formatting'] = format_results
            if not format_results['success']:
                results['errors'].extend(format_results['errors'])

            # Lint 检查
            lint_results = self.run_code_linting(python_files)
            results['lint_valid'] = lint_results['success']
            results['details']['linting'] = lint_results
            if not lint_results['success']:
                results['warnings'].extend(lint_results['warnings'])

            # 类型检查
            type_results = self.run_type_checking(python_files)
            results['type_valid'] = type_results['success']
            results['details']['type_checking'] = type_results
            if not type_results['success']:
                results['warnings'].extend(type_results['warnings'])

            # 自定义规则检查
            custom_results = self.validate_custom_rules(python_files)
            results['custom_rules_valid'] = custom_results['success']
            results['details']['custom_rules'] = custom_results
            if not custom_results['success']:
                results['warnings'].extend(custom_results['warnings'])
                results['errors'].extend([v for v in custom_results['violations'] if 'print(' in v])

        return results


class TestGenerator:
    """测试代码生成引擎"""
    
    def __init__(self, project_root: str, code_generator: Optional[CodeGenerator] = None):
        """
        初始化测试生成器
        
        Args:
            project_root: 项目根目录路径
            code_generator: 代码生成器实例，用于复用配置和模板
        """
        self.project_root = Path(project_root)
        self.code_generator = code_generator or CodeGenerator(project_root)
        
        # 测试文件输出目录
        self.test_output_dirs = {
            'unit': self.project_root / 'tests' / 'unit',
            'integration': self.project_root / 'tests' / 'integration'
        }
        
        # 确保测试目录存在
        for test_dir in self.test_output_dirs.values():
            test_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"TestGenerator initialized with project_root: {self.project_root}")
    
    def generate_tests(self, code_files: List[str]) -> TestGenerationResult:
        """
        为指定的代码文件生成测试
        
        Args:
            code_files: 源代码文件路径列表
            
        Returns:
            TestGenerationResult: 测试生成结果
        """
        start_time = time.time()
        logger.info(f"Starting test generation for {len(code_files)} files")
        
        try:
            test_files = []
            test_cases = []
            errors = []
            warnings = []
            total_test_count = 0
            
            for code_file in code_files:
                try:
                    # 分析源代码结构
                    code_analysis = self._analyze_source_file(code_file)
                    
                    # 生成测试用例
                    file_test_cases = self._generate_test_cases(code_file, code_analysis)
                    test_cases.extend(file_test_cases)
                    
                    # 创建测试文件
                    test_file_path = self._create_test_file(code_file, file_test_cases)
                    if test_file_path:
                        test_files.append(test_file_path)
                        total_test_count += len(file_test_cases)
                    
                except Exception as e:
                    error_msg = f"Failed to generate tests for {code_file}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            # 计算质量评分
            quality_score = self._calculate_quality_score(test_cases, errors)
            
            # 执行生成的测试
            execution_results = {}
            if test_files:
                execution_results = self._execute_tests(test_files)
            
            # 生成覆盖率报告
            coverage_report = {}
            if test_files and not errors:
                coverage_report = self._generate_coverage_report(code_files, test_files)
            
            execution_time = time.time() - start_time
            success = len(errors) == 0 and total_test_count > 0
            
            result = TestGenerationResult(
                success=success,
                test_files=test_files,
                coverage_report=coverage_report,
                test_count=total_test_count,
                quality_score=quality_score,
                execution_results=execution_results,
                errors=errors,
                warnings=warnings
            )
            
            logger.info(f"Test generation completed. Success: {success}, Files: {len(test_files)}, Tests: {total_test_count}")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Test generation failed: {str(e)}"
            logger.error(error_msg)
            
            return TestGenerationResult(
                success=False,
                test_files=[],
                coverage_report={},
                test_count=0,
                quality_score=0.0,
                execution_results={},
                errors=[error_msg],
                warnings=[]
            )
    
    def _analyze_source_file(self, file_path: str) -> Dict[str, Any]:
        """
        分析源代码文件结构
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 代码分析结果
        """
        # 复用 CodeGenerator 的代码分析功能
        return self.code_generator.analyze_code_structure(file_path)
    
    def _generate_test_cases(self, source_file: str, analysis: Dict[str, Any]) -> List[TestCase]:
        """
        基于代码分析结果生成测试用例
        
        Args:
            source_file: 源文件路径
            analysis: 代码分析结果
            
        Returns:
            List[TestCase]: 测试用例列表
        """
        test_cases = []
        
        # 为每个类生成测试
        for class_info in analysis.get('classes', []):
            class_test_cases = self._generate_class_tests(source_file, class_info)
            test_cases.extend(class_test_cases)
        
        # 为每个独立函数生成测试
        for func_info in analysis.get('functions', []):
            func_test_cases = self._generate_function_tests(source_file, func_info)
            test_cases.extend(func_test_cases)
        
        return test_cases
    
    def _generate_class_tests(self, source_file: str, class_info: Dict[str, Any]) -> List[TestCase]:
        """
        为类生成测试用例
        
        Args:
            source_file: 源文件路径
            class_info: 类信息
            
        Returns:
            List[TestCase]: 测试用例列表
        """
        test_cases = []
        class_name = class_info['name']
        
        # 为每个公共方法生成测试
        for method_info in class_info.get('methods', []):
            if not method_info['name'].startswith('_'):  # 只测试公共方法
                method_test_cases = self._generate_method_tests(source_file, class_name, method_info)
                test_cases.extend(method_test_cases)
        
        return test_cases
    
    def _generate_function_tests(self, source_file: str, func_info: Dict[str, Any]) -> List[TestCase]:
        """
        为独立函数生成测试用例
        
        Args:
            source_file: 源文件路径
            func_info: 函数信息
            
        Returns:
            List[TestCase]: 测试用例列表
        """
        func_name = func_info['name']
        args = func_info.get('args', [])
        
        # 生成基本测试用例
        test_case = TestCase(
            name=f"test_{func_name}_basic",
            target_function=func_name,
            test_type="unit",
            arrange_code=self._generate_arrange_code(func_name, args, is_method=False),
            act_code=self._generate_act_code(func_name, args, is_method=False),
            assert_code=self._generate_assert_code(func_name),
            mock_requirements=self._identify_mock_requirements(source_file, func_info),
            fixtures=[]
        )
        
        return [test_case]
    
    def _generate_method_tests(self, source_file: str, class_name: str, method_info: Dict[str, Any]) -> List[TestCase]:
        """
        为类方法生成测试用例
        
        Args:
            source_file: 源文件路径
            class_name: 类名
            method_info: 方法信息
            
        Returns:
            List[TestCase]: 测试用例列表
        """
        method_name = method_info['name']
        args = method_info.get('args', [])
        
        # 生成基本测试用例
        test_case = TestCase(
            name=f"test_{class_name.lower()}_{method_name}_basic",
            target_function=f"{class_name}.{method_name}",
            test_type="unit",
            arrange_code=self._generate_arrange_code(method_name, args, is_method=True, class_name=class_name),
            act_code=self._generate_act_code(method_name, args, is_method=True, class_name=class_name),
            assert_code=self._generate_assert_code(method_name),
            mock_requirements=self._identify_mock_requirements(source_file, method_info),
            fixtures=[]
        )
        
        return [test_case]
    
    def _generate_arrange_code(self, func_name: str, args: List[str], is_method: bool = False, class_name: str = None) -> str:
        """生成 Arrange 阶段代码"""
        if is_method and class_name:
            # 为类方法生成实例创建代码
            arrange_lines = [
                f"# Arrange",
                f"instance = {class_name}()"
            ]
            
            # 为参数生成测试数据
            for arg in args:
                if arg != 'self':
                    arrange_lines.append(f"{arg} = None  # TODO: Provide test data")
        else:
            # 为独立函数生成参数准备代码
            arrange_lines = ["# Arrange"]
            for arg in args:
                arrange_lines.append(f"{arg} = None  # TODO: Provide test data")
        
        return "\n        ".join(arrange_lines)
    
    def _generate_act_code(self, func_name: str, args: List[str], is_method: bool = False, class_name: str = None) -> str:
        """生成 Act 阶段代码"""
        act_lines = ["# Act"]
        
        if is_method and class_name:
            # 调用类方法
            method_args = [arg for arg in args if arg != 'self']
            if method_args:
                args_str = ", ".join(method_args)
                act_lines.append(f"result = instance.{func_name}({args_str})")
            else:
                act_lines.append(f"result = instance.{func_name}()")
        else:
            # 调用独立函数
            if args:
                args_str = ", ".join(args)
                act_lines.append(f"result = {func_name}({args_str})")
            else:
                act_lines.append(f"result = {func_name}()")
        
        return "\n        ".join(act_lines)
    
    def _generate_assert_code(self, func_name: str) -> str:
        """生成 Assert 阶段代码"""
        return "\n        ".join([
            "# Assert",
            "assert result is not None  # TODO: Add specific assertions",
            "# TODO: Add more specific test assertions based on expected behavior"
        ])
    
    def _identify_mock_requirements(self, source_file: str, func_info: Dict[str, Any]) -> List[str]:
        """
        识别需要 Mock 的依赖
        
        Args:
            source_file: 源文件路径
            func_info: 函数/方法信息
            
        Returns:
            List[str]: 需要 Mock 的依赖列表
        """
        mock_requirements = []
        
        # 基于代码分析识别常见的需要 Mock 的模块
        common_mock_targets = {
            'os': 'os operations',
            'subprocess': 'subprocess calls',
            'pathlib.Path': 'file system operations',
            'datetime.datetime': 'time operations',
            'logging': 'logging operations',
            'sqlite3': 'database operations'
        }
        
        try:
            full_path = self.project_root / source_file
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查代码中是否使用了需要 Mock 的模块
            for module, description in common_mock_targets.items():
                if module in content:
                    mock_requirements.append(module)
        
        except Exception as e:
            logger.warning(f"Failed to analyze mock requirements for {source_file}: {str(e)}")
        
        return mock_requirements
    
    def _create_test_file(self, source_file: str, test_cases: List[TestCase]) -> Optional[str]:
        """
        创建测试文件
        
        Args:
            source_file: 源文件路径
            test_cases: 测试用例列表
            
        Returns:
            Optional[str]: 创建的测试文件路径，失败时返回 None
        """
        if not test_cases:
            return None
        
        try:
            # 生成测试文件名
            source_path = Path(source_file)
            test_filename = f"test_{source_path.stem}.py"
            test_file_path = self.test_output_dirs['unit'] / test_filename
            
            # 生成测试文件内容
            test_content = self._generate_test_file_content(source_file, test_cases)
            
            # 写入测试文件
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            logger.info(f"Created test file: {test_file_path}")
            return str(test_file_path.relative_to(self.project_root))
            
        except Exception as e:
            logger.error(f"Failed to create test file for {source_file}: {str(e)}")
            return None
    
    def _generate_test_file_content(self, source_file: str, test_cases: List[TestCase]) -> str:
        """
        生成测试文件内容
        
        Args:
            source_file: 源文件路径
            test_cases: 测试用例列表
            
        Returns:
            str: 测试文件内容
        """
        source_path = Path(source_file)
        module_name = source_file.replace('/', '.').replace('.py', '')
        
        # 生成导入语句
        imports = [
            "import pytest",
            "from unittest.mock import Mock, patch",
            f"from {module_name} import *",
            ""
        ]
        
        # 生成测试类或函数
        test_methods = []
        for test_case in test_cases:
            test_method = self._generate_test_method(test_case)
            test_methods.append(test_method)
        
        # 组合完整的测试文件内容
        content_parts = [
            '"""',
            f'Unit tests for {source_file}',
            'Generated automatically by TestGenerator',
            '"""',
            "",
            "\n".join(imports),
            "\n\n".join(test_methods)
        ]
        
        return "\n".join(content_parts)
    
    def _generate_test_method(self, test_case: TestCase) -> str:
        """
        生成单个测试方法
        
        Args:
            test_case: 测试用例
            
        Returns:
            str: 测试方法代码
        """
        method_lines = [
            f"def {test_case.name}():",
            f'    """Test {test_case.target_function} - {test_case.test_type} test"""'
        ]
        
        # 添加 Mock 装饰器
        if test_case.mock_requirements:
            for mock_target in test_case.mock_requirements:
                method_lines.insert(0, f'@patch("{mock_target}")')
        
        # 添加测试代码
        if test_case.arrange_code:
            method_lines.append(f"    {test_case.arrange_code}")
        
        if test_case.act_code:
            method_lines.append(f"    {test_case.act_code}")
        
        if test_case.assert_code:
            method_lines.append(f"    {test_case.assert_code}")
        
        return "\n".join(method_lines)
    
    def _calculate_quality_score(self, test_cases: List[TestCase], errors: List[str]) -> float:
        """
        计算测试质量评分
        
        Args:
            test_cases: 测试用例列表
            errors: 错误列表
            
        Returns:
            float: 质量评分 (0.0-1.0)
        """
        if not test_cases:
            return 0.0
        
        base_score = 0.5  # 基础分数
        
        # 根据测试用例数量调整分数
        test_count_score = min(len(test_cases) / 10.0, 0.3)
        
        # 根据错误数量扣分
        error_penalty = min(len(errors) * 0.1, 0.4)
        
        # 检查是否有断言代码
        has_assertions = sum(1 for tc in test_cases if "assert" in tc.assert_code) / len(test_cases) * 0.2
        
        final_score = base_score + test_count_score + has_assertions - error_penalty
        return max(0.0, min(1.0, final_score))
    
    def _execute_tests(self, test_files: List[str]) -> Dict[str, Any]:
        """
        执行生成的测试
        
        Args:
            test_files: 测试文件路径列表
            
        Returns:
            Dict[str, Any]: 测试执行结果
        """
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': [],
            'warnings': [],
            'execution_time': 0.0
        }
        
        try:
            # 构建 pytest 命令
            test_paths = [str(self.project_root / tf) for tf in test_files]
            cmd = ['python', '-m', 'pytest', '-v', '--tb=short'] + test_paths
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            execution_time = time.time() - start_time
            
            results['execution_time'] = execution_time
            
            if result.returncode == 0:
                # 解析成功的测试结果
                output = result.stdout
                results['passed'] = output.count(' PASSED')
                results['total_tests'] = results['passed']
                logger.info(f"All {results['passed']} tests passed")
            else:
                # 解析失败的测试结果
                output = result.stdout + result.stderr
                results['passed'] = output.count(' PASSED')
                results['failed'] = output.count(' FAILED')
                results['total_tests'] = results['passed'] + results['failed']
                results['errors'].append(f"Some tests failed: {result.stderr}")
                logger.warning(f"Test execution completed with failures: {results['failed']}")
            
        except subprocess.TimeoutExpired:
            results['errors'].append("Test execution timed out")
            logger.error("Test execution timed out")
        except FileNotFoundError:
            results['errors'].append("pytest not found - please install pytest")
            logger.error("pytest not found")
        except Exception as e:
            results['errors'].append(f"Test execution failed: {str(e)}")
            logger.error(f"Test execution failed: {str(e)}")
        
        return results
    
    def _generate_coverage_report(self, source_files: List[str], test_files: List[str]) -> Dict[str, Any]:
        """
        生成覆盖率报告
        
        Args:
            source_files: 源文件路径列表
            test_files: 测试文件路径列表
            
        Returns:
            Dict[str, Any]: 覆盖率报告
        """
        coverage_report = {
            'total_coverage': 0.0,
            'file_coverage': {},
            'uncovered_lines': {},
            'errors': [],
            'warnings': []
        }
        
        try:
            # 构建 coverage 命令
            test_paths = [str(self.project_root / tf) for tf in test_files]
            source_paths = [str(self.project_root / sf) for sf in source_files]
            
            # 运行覆盖率分析
            cmd = ['python', '-m', 'coverage', 'run', '--source'] + source_paths + ['-m', 'pytest'] + test_paths
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                # 生成覆盖率报告
                report_cmd = ['python', '-m', 'coverage', 'report', '--show-missing']
                report_result = subprocess.run(report_cmd, capture_output=True, text=True, timeout=30)
                
                if report_result.returncode == 0:
                    # 解析覆盖率报告
                    coverage_report.update(self._parse_coverage_output(report_result.stdout))
                    logger.info(f"Coverage analysis completed: {coverage_report['total_coverage']:.1%}")
                else:
                    coverage_report['errors'].append("Failed to generate coverage report")
            else:
                coverage_report['errors'].append("Failed to run coverage analysis")
        
        except Exception as e:
            coverage_report['errors'].append(f"Coverage analysis failed: {str(e)}")
            logger.error(f"Coverage analysis failed: {str(e)}")
        
        return coverage_report
    
    def _parse_coverage_output(self, coverage_output: str) -> Dict[str, Any]:
        """
        解析覆盖率输出
        
        Args:
            coverage_output: coverage 命令输出
            
        Returns:
            Dict[str, Any]: 解析后的覆盖率数据
        """
        parsed_data = {
            'total_coverage': 0.0,
            'file_coverage': {},
            'uncovered_lines': {}
        }
        
        try:
            lines = coverage_output.strip().split('\n')
            for line in lines:
                if '%' in line and '---' not in line and 'Name' not in line and 'TOTAL' not in line:
                    parts = line.split()
                    if len(parts) >= 4:
                        filename = parts[0]
                        coverage_pct = parts[3].replace('%', '')
                        try:
                            parsed_data['file_coverage'][filename] = float(coverage_pct) / 100.0
                        except ValueError:
                            continue
                elif 'TOTAL' in line:
                    parts = line.split()
                    if len(parts) >= 4:
                        total_pct = parts[3].replace('%', '')
                        try:
                            parsed_data['total_coverage'] = float(total_pct) / 100.0
                        except ValueError:
                            pass
        
        except Exception as e:
            logger.warning(f"Failed to parse coverage output: {str(e)}")
        
        return parsed_data


class TestExecutor:
    """测试执行引擎"""
    
    def __init__(self, project_root: str):
        """
        初始化测试执行器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root)
        logger.info(f"TestExecutor initialized with project_root: {self.project_root}")
    
    def execute_tests(self, test_files: List[str], pytest_args: List[str] = None) -> Dict[str, Any]:
        """
        执行测试文件
        
        Args:
            test_files: 测试文件路径列表
            pytest_args: 额外的 pytest 参数
            
        Returns:
            Dict[str, Any]: 测试执行结果
        """
        start_time = time.time()
        logger.info(f"Executing {len(test_files)} test files")
        
        results = {
            'success': False,
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'errors': [],
            'warnings': [],
            'execution_time': 0.0,
            'detailed_results': [],
            'coverage_info': {}
        }
        
        try:
            # 构建 pytest 命令
            test_paths = [str(self.project_root / tf) for tf in test_files]
            cmd = ['python3', '-m', 'pytest', '-v', '--tb=short']
            
            # 添加额外参数
            if pytest_args:
                cmd.extend(pytest_args)
            
            cmd.extend(test_paths)
            
            # 执行测试
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            execution_time = time.time() - start_time
            results['execution_time'] = execution_time
            
            # 解析结果
            stdout = result.stdout
            stderr = result.stderr
            
            # 分析输出
            results.update(self._parse_pytest_output(stdout, stderr))
            results['success'] = result.returncode == 0
            
            if results['success']:
                logger.info(f"All tests passed: {results['passed']} tests in {execution_time:.2f}s")
            else:
                logger.warning(f"Tests failed: {results['failed']} failed, {results['passed']} passed")
                if stderr:
                    results['errors'].append(f"Test execution errors: {stderr}")
            
        except subprocess.TimeoutExpired:
            results['errors'].append("Test execution timed out after 2 minutes")
            logger.error("Test execution timeout")
        except FileNotFoundError:
            results['errors'].append("pytest not found - please install pytest")
            logger.error("pytest not found")
        except Exception as e:
            results['errors'].append(f"Test execution failed: {str(e)}")
            logger.error(f"Test execution failed: {str(e)}")
        finally:
            results['execution_time'] = time.time() - start_time
        
        return results
    
    def execute_with_coverage(self, test_files: List[str], source_files: List[str]) -> Dict[str, Any]:
        """
        执行测试并生成覆盖率报告
        
        Args:
            test_files: 测试文件路径列表
            source_files: 源文件路径列表
            
        Returns:
            Dict[str, Any]: 测试执行结果包含覆盖率信息
        """
        logger.info(f"Executing tests with coverage for {len(source_files)} source files")
        
        # 先执行常规测试
        results = self.execute_tests(test_files, ['--cov=' + ','.join(source_files), '--cov-report=term'])
        
        # 生成详细覆盖率报告
        if results['success'] or results['passed'] > 0:
            coverage_results = self._generate_coverage_report(source_files)
            results['coverage_info'] = coverage_results
        
        return results
    
    def _parse_pytest_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        解析 pytest 输出
        
        Args:
            stdout: 标准输出
            stderr: 错误输出
            
        Returns:
            Dict[str, Any]: 解析后的结果
        """
        parsed = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'detailed_results': []
        }
        
        try:
            lines = stdout.split('\n')
            for line in lines:
                # 解析测试结果行
                if ' PASSED' in line:
                    parsed['passed'] += 1
                    parsed['detailed_results'].append({
                        'test': line.split('::')[1] if '::' in line else line,
                        'status': 'PASSED'
                    })
                elif ' FAILED' in line:
                    parsed['failed'] += 1
                    parsed['detailed_results'].append({
                        'test': line.split('::')[1] if '::' in line else line,
                        'status': 'FAILED'
                    })
                elif ' SKIPPED' in line:
                    parsed['skipped'] += 1
                    parsed['detailed_results'].append({
                        'test': line.split('::')[1] if '::' in line else line,
                        'status': 'SKIPPED'
                    })
                # 解析总结行（例如：====== 2 passed, 1 failed in 1.23s ======）
                elif 'passed' in line and ('failed' in line or 'error' in line or 's =====' in line):
                    # 提取数字
                    import re
                    numbers = re.findall(r'(\d+)\s+(\w+)', line)
                    for num, status in numbers:
                        if status in ['passed', 'failed', 'skipped', 'error']:
                            parsed[status] = int(num)
            
            parsed['total_tests'] = parsed['passed'] + parsed['failed'] + parsed['skipped']
            
        except Exception as e:
            logger.warning(f"Failed to parse pytest output: {str(e)}")
        
        return parsed
    
    def _generate_coverage_report(self, source_files: List[str]) -> Dict[str, Any]:
        """
        生成覆盖率报告
        
        Args:
            source_files: 源文件路径列表
            
        Returns:
            Dict[str, Any]: 覆盖率报告
        """
        coverage_report = {
            'total_coverage': 0.0,
            'file_coverage': {},
            'uncovered_lines': {},
            'errors': []
        }
        
        try:
            # 生成覆盖率报告
            cmd = ['python3', '-m', 'coverage', 'report', '--show-missing']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                coverage_report.update(self._parse_coverage_output(result.stdout))
                logger.info(f"Coverage report generated: {coverage_report.get('total_coverage', 0):.1%}")
            else:
                coverage_report['errors'].append("Failed to generate coverage report")
                logger.error("Coverage report generation failed")
        
        except Exception as e:
            coverage_report['errors'].append(f"Coverage analysis failed: {str(e)}")
            logger.error(f"Coverage analysis failed: {str(e)}")
        
        return coverage_report
    
    def _parse_coverage_output(self, coverage_output: str) -> Dict[str, Any]:
        """
        解析覆盖率输出
        
        Args:
            coverage_output: coverage 命令输出
            
        Returns:
            Dict[str, Any]: 解析后的覆盖率数据
        """
        parsed_data = {
            'total_coverage': 0.0,
            'file_coverage': {},
            'uncovered_lines': {}
        }
        
        try:
            lines = coverage_output.strip().split('\n')
            for line in lines:
                if '%' in line and '---' not in line and 'Name' not in line:
                    parts = line.split()
                    if len(parts) >= 4:
                        if 'TOTAL' in line:
                            # 总覆盖率行
                            total_pct = parts[3].replace('%', '')
                            try:
                                parsed_data['total_coverage'] = float(total_pct) / 100.0
                            except ValueError:
                                pass
                        else:
                            # 单个文件覆盖率行
                            filename = parts[0]
                            coverage_pct = parts[3].replace('%', '')
                            try:
                                parsed_data['file_coverage'][filename] = float(coverage_pct) / 100.0
                            except ValueError:
                                continue
                            
                            # 提取未覆盖的行号
                            if len(parts) > 4:
                                uncovered = parts[4] if parts[4] != '0' else ''
                                if uncovered:
                                    parsed_data['uncovered_lines'][filename] = uncovered
        
        except Exception as e:
            logger.warning(f"Failed to parse coverage output: {str(e)}")
        
        return parsed_data
