from __future__ import annotations
import re
import logging
import subprocess
import shutil
import yaml
from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Optional, Tuple

logger = logging.getLogger(__name__)


@dataclass
class ShardingResult:
    success: bool
    sharded_files: List[str] = field(default_factory=list)
    index_file: Optional[str] = None
    errors: List[str] = field(default_factory=list)


@dataclass
class ValidationResult:
    valid: bool
    issues: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


def _kebab_case(s: str) -> str:
    s = s.strip().lower()
    s = re.sub(r"[^\w\s-]", "", s, flags=re.UNICODE)
    s = re.sub(r"\s+", "-", s)
    s = re.sub(r"-{2,}", "-", s)
    s = s.strip("-")
    if not s:
        return "untitled"
    return s


def _adjust_heading_levels(content_lines: List[str], decrease: int = 1) -> List[str]:
    out: List[str] = []
    pattern = re.compile(r"^(#{1,6})\s")
    for ln in content_lines:
        m = pattern.match(ln)
        if m:
            hashes = m.group(1)
            new_count = max(1, len(hashes) - decrease)
            ln = re.sub(r"^#{1,6}", "#" * new_count, ln, count=1)
        out.append(ln)
    return out


def _split_by_h2(markdown: str) -> List[Tuple[str, str]]:
    lines = markdown.splitlines()
    chunks: List[Tuple[str, List[str]]] = []
    current: List[str] = []
    current_heading: Optional[str] = None
    in_fence = False
    fence_marker: Optional[str] = None

    h2_pattern = re.compile(r"^##\s+(.*)")

    for ln in lines:
        fence_m = re.match(r"^(`{3,}|~{3,})(.*)$", ln)
        if fence_m:
            marker = fence_m.group(1)
            if not in_fence:
                in_fence = True
                fence_marker = marker
            else:
                if marker == fence_marker:
                    in_fence = False
                    fence_marker = None
            current.append(ln)
            continue

        if not in_fence:
            m = h2_pattern.match(ln)
            if m:
                heading = m.group(1).strip()
                if current_heading is None and current:
                    chunks.append(("intro", current.copy()))
                elif current_heading is not None:
                    chunks.append((current_heading, current.copy()))
                current = [ln]
                current_heading = heading
                continue

        current.append(ln)

    if current:
        if current_heading is None:
            chunks.append(("intro", current.copy()))
        else:
            chunks.append((current_heading, current.copy()))

    return [(h, "\n".join(c)) for h, c in chunks]


def _try_md_tree_explode(src: Path, out_dir: Path) -> Optional[ShardingResult]:
    """
    Try to use the external `md-tree` (markdown-tree-parser) tool to explode the markdown into a tree.
    Returns a ShardingResult on success, or None if the tool is not configured/available or failed.

    Behavior changes:
    - Search for `.bmad-core/core-config.yaml` starting from the source file's directory and walking
      upward to filesystem root. This ensures tests that create config in tmp_path are respected.
    - If md-tree runs but produces no .md files, treat as failure (return None) so fallback logic is used.
    - Capture stderr on failure for diagnostics (logged).
    """
    try:
        # find core-config.yaml by searching upward from the source file location
        config_path: Optional[Path] = None
        current = src.resolve().parent
        root = Path(current.root) if hasattr(current, "root") else Path(current.anchor)
        while True:
            candidate = current / ".bmad-core" / "core-config.yaml"
            if candidate.exists():
                config_path = candidate
                break
            if current == current.parent or str(current.resolve()) == str(root.resolve()):
                break
            current = current.parent

        if config_path is None:
            logger.debug("core-config.yaml not found starting from %s, skipping md-tree explode", src)
            return None

        cfg = {}
        try:
            with open(config_path, "r", encoding="utf-8") as fh:
                cfg = yaml.safe_load(fh) or {}
        except Exception as e:
            logger.warning("Failed to read core-config.yaml at %s: %s", config_path, e)
            return None

        if not cfg.get("markdownExploder", False):
            logger.debug("markdownExploder disabled in core-config.yaml at %s", config_path)
            return None

        # ensure output directory exists
        out_dir.mkdir(parents=True, exist_ok=True)

        # check md-tree availability
        md_tree_cmd = shutil.which("md-tree")
        if not md_tree_cmd:
            logger.warning("md-tree not found in PATH; please install @kayvan/markdown-tree-parser")
            return None

        # call: md-tree explode {input} {output}
        try:
            proc = subprocess.run([md_tree_cmd, "explode", str(src), str(out_dir)],
                                  check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            logger.info("md-tree explode stdout: %s", proc.stdout.strip())
        except subprocess.CalledProcessError as cpe:
            # capture stderr for diagnostics and return None to indicate fallback,
            # but include stderr in returned ShardingResult.errors when possible.
            stderr_text = (cpe.stderr or "").strip()
            logger.warning("md-tree explode failed with returncode %s: %s", getattr(cpe, "returncode", "??"), stderr_text)
            return ShardingResult(success=False, sharded_files=[], index_file=None, errors=[f"md-tree error: {stderr_text}"])

        # collect generated files (assume md-tree writes .md files into out_dir)
        generated = []
        for p in sorted(out_dir.glob("*.md")):
            generated.append(str(p))

        if not generated:
            logger.warning("md-tree ran but produced no .md files in %s", out_dir)
            return None

        index_path = out_dir / "index.md"
        index_file = str(index_path) if index_path.exists() else None

        return ShardingResult(success=True, sharded_files=generated, index_file=index_file, errors=[])
    except Exception as e:
        logger.exception("Unexpected error while attempting md-tree explode: %s", e)
        return None


# Note: removed duplicate earlier shard_documents definition to keep a single implementation.


def shard_documents(docs_path: str) -> ShardingResult:
    """
    Shard the provided markdown file by '##' headings, with optional use of md-tree explode
    when enabled via a nearby .bmad-core/core-config.yaml.

    - docs_path: path to the source markdown file (e.g. 'docs/prd.md' or 'docs/architecture.md')
    - writes files to docs/prd/ or docs/architecture/ respectively.
    - returns ShardingResult with list of generated files and index.md
    """
    try:
        src = Path(docs_path)
        if not src.exists():
            msg = f"Source file does not exist: {docs_path}"
            logger.error(msg)
            return ShardingResult(success=False, errors=[msg])

        # determine output directory
        if "prd" in src.parts or src.name.lower().startswith("prd"):
            out_dir = src.parent / "prd"
        elif "architecture" in src.parts or src.name.lower().startswith("architecture"):
            out_dir = src.parent / "architecture"
        else:
            out_dir = src.parent / src.stem

        # First try markdown-tree-parser (md-tree) if configured for this project tree
        try_result = _try_md_tree_explode(src, out_dir)
        if try_result:
            logger.info("Used md-tree explode for sharding: %s", src)
            return try_result

        # Fallback to built-in splitting logic
        out_dir.mkdir(parents=True, exist_ok=True)

        text = src.read_text(encoding="utf-8")

        chunks = _split_by_h2(text)
        generated: List[str] = []
        index_lines: List[str] = ["# Index", ""]

        for heading, content in chunks:
            if heading == "intro":
                file_stem = "intro"
                title_for_file = "Introduction"
            else:
                file_stem = _kebab_case(heading)
                title_for_file = heading

            filename = f"{file_stem}.md"
            target = out_dir / filename

            content_lines = content.splitlines()
            adjusted_lines = _adjust_heading_levels(content_lines, decrease=1)

            if adjusted_lines:
                first_line = adjusted_lines[0]
                if not first_line.lstrip().startswith("#"):
                    adjusted_lines.insert(0, f"# {title_for_file}")
                else:
                    m = re.match(r"^(#{1,6})\s+(.*)$", adjusted_lines[0])
                    if m:
                        hashes = m.group(1)
                        adjusted_lines[0] = f"{hashes} {title_for_file}"

            final_content = "\n".join(adjusted_lines).rstrip() + "\n"

            try:
                target.write_text(final_content, encoding="utf-8")
                generated.append(str(target))
                index_lines.append(f"- [{title_for_file}]({filename})")
            except Exception as e:
                logger.exception("Failed to write shard file %s", target)
                return ShardingResult(success=False, errors=[str(e)])

        index_path = out_dir / "index.md"
        index_text = "\n".join(index_lines).rstrip() + "\n"
        index_path.write_text(index_text, encoding="utf-8")

        return ShardingResult(success=True, sharded_files=generated, index_file=str(index_path))
    except Exception as exc:
        logger.exception("Unexpected error in shard_documents")
        return ShardingResult(success=False, errors=[str(exc)])


def validate_document_structure(doc_path: str) -> ValidationResult:
    issues: List[str] = []
    warnings: List[str] = []
    try:
        p = Path(doc_path)
        if not p.exists():
            issues.append(f"File not found: {doc_path}")
            return ValidationResult(valid=False, issues=issues, warnings=warnings)

        text = p.read_text(encoding="utf-8")
        if not text.strip():
            issues.append(f"File is empty: {doc_path}")
            return ValidationResult(valid=False, issues=issues, warnings=warnings)

        if not re.search(r"^#{1,6}\s+", text, flags=re.MULTILINE):
            issues.append(f"No markdown headings found in file: {doc_path}")
 
        # Improved fenced code block detection: look for start/end pairs using line-oriented search.
        fence_lines = [m for m in re.finditer(r"^(`{3,}|~{3,})(.*)$", text, flags=re.MULTILINE)]
        if fence_lines:
            # Count fence starts and ends by pairing markers in order.
            stack: List[str] = []
            for m in fence_lines:
                marker = m.group(1)
                if not stack or stack[-1] != marker:
                    stack.append(marker)
                else:
                    stack.pop()
            if stack:
                warnings.append("Possible unclosed fenced code block")

        valid = len(issues) == 0
        return ValidationResult(valid=valid, issues=issues, warnings=warnings)
    except Exception as e:
        logger.exception("Validation failed for %s", doc_path)
        return ValidationResult(valid=False, issues=[str(e)], warnings=warnings)

# --- Epic extraction and file generation (Story 1.3) ---
from typing import Dict
try:
    from workflow.models import Epic, Story  # type: ignore
except Exception:
    # fallback import path if module is executed as script
    from models import Epic, Story  # type: ignore


def extract_epics(prd_shards: List[str]) -> List[Epic]:
    """
    Parse provided PRD shard file paths (strings) and extract Epic and Story structures.

    - prd_shards: list of file paths (strings) pointing to markdown shard files (e.g. docs/prd/<file>.md)
    - Returns: list of Epic objects

    The parser recognizes:
      - Epic headings: lines starting with "## Epic {N}:" (case-insensitive)
      - Story headings: lines starting with "### Story {N}.{M}:" under an Epic
      - Acceptance criteria: numbered list lines (e.g., "1. ...") following a story section
    This is intentionally conservative and best-effort; it will skip malformed sections.
    """
    epics: List[Epic] = []
    epic_pattern = re.compile(r"^##\s*Epic\s*([0-9A-Za-z\-.]+)\s*:\s*(.*)$", flags=re.IGNORECASE)
    story_pattern = re.compile(r"^###\s*Story\s*([\d\.A-Za-z\-]+)\s*:\s*(.*)$", flags=re.IGNORECASE)
    ac_pattern = re.compile(r"^\s*\d+\.\s+(.*)$")
    for shard in prd_shards:
        try:
            p = Path(shard)
            if not p.exists():
                logger.debug("Shard file not found, skipping: %s", shard)
                continue
            text = p.read_text(encoding="utf-8")
        except Exception as e:
            logger.exception("Failed reading shard %s: %s", shard, e)
            continue

        # split by lines and iterate, building epics
        lines = text.splitlines()
        current_epic: Optional[Dict] = None
        current_story: Optional[Dict] = None

        def flush_current_story():
            nonlocal current_story, current_epic
            if current_story and current_epic is not None:
                try:
                    story_obj = Story(
                        story_id=current_story.get("story_id") or "",
                        title=current_story.get("title") or "",
                        user_story=current_story.get("user_story") or "",
                        acceptance_criteria=current_story.get("acceptance_criteria", []),
                        priority=current_story.get("priority"),
                        estimated_days=current_story.get("estimated_days"),
                        dependencies=current_story.get("dependencies", []),
                        status=current_story.get("status"),
                    )
                    current_epic["stories"].append(story_obj)
                except Exception:
                    logger.exception("Failed to create Story object for %s", current_story)
                finally:
                    current_story = None

        def flush_current_epic():
            nonlocal current_epic, epics
            if current_epic:
                try:
                    epic_obj = Epic(
                        epic_id=str(current_epic.get("epic_id") or ""),
                        title=current_epic.get("title") or "",
                        description=current_epic.get("description") or "",
                        stories=current_epic.get("stories", []),
                        dependencies=current_epic.get("dependencies", []),
                        priority=current_epic.get("priority"),
                        estimated_days=current_epic.get("estimated_days"),
                        status=current_epic.get("status"),
                    )
                    epics.append(epic_obj)
                except Exception:
                    logger.exception("Failed to create Epic object for %s", current_epic)
                finally:
                    current_epic = None

        i = 0
        while i < len(lines):
            ln = lines[i]
            # Epic start
            m_ep = epic_pattern.match(ln)
            if m_ep:
                # flush previous epic
                flush_current_story()
                flush_current_epic()
                epic_id = m_ep.group(1).strip()
                title = m_ep.group(2).strip()
                current_epic = {
                    "epic_id": epic_id,
                    "title": title,
                    "description": "",
                    "stories": [],
                    "dependencies": [],
                    "priority": None,
                    "estimated_days": None,
                    "status": None,
                }
                # collect following lines as description until next Story or next Epic
                desc_lines: List[str] = []
                j = i + 1
                while j < len(lines):
                    if epic_pattern.match(lines[j]) or story_pattern.match(lines[j]):
                        break
                    desc_lines.append(lines[j])
                    j += 1
                current_epic["description"] = "\n".join(desc_lines).strip()
                i = j
                continue

            # Story start
            m_st = story_pattern.match(ln)
            if m_st and current_epic is not None:
                # flush previous story
                flush_current_story()
                story_id = m_st.group(1).strip()
                story_title = m_st.group(2).strip()
                current_story = {
                    "story_id": story_id,
                    "title": story_title,
                    "user_story": "",
                    "acceptance_criteria": [],
                    "priority": None,
                    "estimated_days": None,
                    "dependencies": [],
                    "status": None,
                }
                # scan story block for acceptance criteria and simple user story lines
                j = i + 1
                user_story_lines: List[str] = []
                ac_lines: List[str] = []
                while j < len(lines):
                    if story_pattern.match(lines[j]) or epic_pattern.match(lines[j]):
                        break
                    # try to detect a line like "**用户故事：** As a ..." or "As a"
                    low = lines[j].strip()
                    if low.lower().startswith("**用户故事") or low.lower().startswith("**user story"):
                        # capture inline after colon if present
                        parts = low.split("**", 3)
                        # fallback: take remainder of line
                        if ":" in low:
                            user_story_lines.append(low.split(":", 1)[1].strip())
                    elif low.lower().startswith("as a ") or low.lower().startswith("- as a ") or low.lower().startswith("as an "):
                        user_story_lines.append(low)
                    # acceptance criteria lines like "1. something"
                    ac_m = ac_pattern.match(low)
                    if ac_m:
                        ac_lines.append(ac_m.group(1).strip())
                    j += 1
                if user_story_lines:
                    current_story["user_story"] = "\n".join(user_story_lines).strip()
                if ac_lines:
                    current_story["acceptance_criteria"] = ac_lines
                i = j
                continue

            i += 1

        # flush at end of file
        flush_current_story()
        flush_current_epic()

    return epics


def create_epic_files(epics: List[Epic], out_dir: str) -> List[str]:
    """
    Generate standardized Epic markdown files under out_dir (string path).
    Returns list of generated file paths as strings.
    """
    generated: List[str] = []
    try:
        out_path = Path(out_dir)
        out_path.mkdir(parents=True, exist_ok=True)
        for epic in epics:
            # normalize file name epic-{N}.md using epic.epic_id
            file_stem = f"epic-{_kebab_case(epic.epic_id)}"
            filename = f"{file_stem}.md"
            target = out_path / filename
            lines: List[str] = []
            # Header
            lines.append(f"# Epic {epic.epic_id}: {epic.title}")
            lines.append("")
            lines.append("## Epic 目标")
            lines.append(epic.description or "")
            lines.append("")
            lines.append("## Story 列表")
            lines.append("")
            for s in epic.stories:
                lines.append(f"### Story {s.story_id}: {s.title}")
                lines.append(f"**用户故事：** {s.user_story or ''}")
                lines.append("")
                lines.append("**验收标准：**")
                if s.acceptance_criteria:
                    for idx, ac in enumerate(s.acceptance_criteria, start=1):
                        lines.append(f"{idx}. {ac}")
                else:
                    lines.append("（未提供）")
                lines.append("")
                lines.append(f"**优先级：** {s.priority or '未定义'}")
                lines.append(f"**估算：** {s.estimated_days or '未提供'}")
                lines.append(f"**依赖：** {', '.join(s.dependencies) if s.dependencies else '无'}")
                lines.append("")
            # Epic completion and metadata
            lines.append("## Epic 完成标准")
            lines.append("")
            lines.append("")
            lines.append("## 技术注意事项")
            lines.append("")
            lines.append("## 风险和缓解措施")
            lines.append("")
            final = "\n".join(lines).rstrip() + "\n"
            try:
                target.write_text(final, encoding="utf-8")
                generated.append(str(target))
            except Exception as e:
                logger.exception("Failed to write epic file %s: %s", target, e)
                continue
        # create index
        index_path = out_path / "index.md"
        index_lines = ["# Epics", ""]
        for p in sorted(out_path.glob("epic-*.md")):
            title = p.read_text(encoding="utf-8").splitlines()[0].lstrip("# ").strip() if p.exists() else p.stem
            index_lines.append(f"- [{title}]({p.name})")
        index_text = "\n".join(index_lines).rstrip() + "\n"
        index_path.write_text(index_text, encoding="utf-8")
        return generated
    except Exception as e:
        logger.exception("create_epic_files failed: %s", e)
        return generated